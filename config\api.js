/**
 * base    : 基础业务API
 * buyer   : 买家API
 * inst    : 金融API
 */
// 开发环境
const dev = {
  common: "http://192.168.1.221:8890",
  buyer: "http://192.168.1.221:8888",
  inst: "http://192.168.1.82:9301",
  im: "http://192.168.1.221:8885",
};

// 开发环境
const test = {
  common: "http://192.168.1.221:8890",
  buyer: "http://192.168.1.221:8888",
  inst: "http://192.168.1.82:9301",
  im: "http://192.168.1.221:8885",
};


const prod = {
  common: "http://192.168.1.221:8890",
  buyer: "http://192.168.1.221:8888",
  inst: "http://192.168.1.82:9301",
  im: "http://192.168.1.221:8885",
};

// const prod = {
//   common: "https://common.api.zhenxiaoxuan.com",
//   buyer: "https://buyer.api.zhenxiaoxuan.com",
//   inst: "http://192.168.1.82:9301",
//   im: "https://im.api.zhenxiaoxuan.com",
// };

//默认开发环境
let api = prod;
//如果是开发环境
if (process.env.NODE_ENV == "development") {
  api = dev;
} else if(process.env.NODE_ENV == "test") {
  api = test;
}
//微信小程序，app的打包方式建议为生产环境，所以这块直接条件编译赋值
// #ifdef MP-WEIXIN || APP-PLUS
api = prod;
// #endif

api.buyer += "/buyer";
// api.inst += "/inst"; // 注释掉，inst不需要额外的前缀
api.common += "/common";
api.im += "/im";
export default {
  ...api,
};
