<template>
  <view class="bill-page">
    <!-- 顶部提示 -->
    <view class="bill-tip">
      <image src="/static/financial/volume.png" mode="scaleToFill" />
      <view class="bill-tip-divider"></view>
      <text>逾期将上报征信，请保持良好的信用习惯</text>
    </view>
    <!-- 额度信息 -->
    <view class="bill-amount-box">
      <view class="bill-amount-main">
        <text class="bill-amount-main-label">当前待还总额(元)</text>
        <text class="bill-amount-main-value">{{ billDetail && billDetail.waitRepayAmount || '0.00' }}</text>
      </view>
      <view class="bill-amount-sub">
        <view class="bill-amount-sub-item">
          <text class="bill-amount-sub-value">{{ billDetail && billDetail.usedAuAmount || '0.00' }}</text>
          <view class="bill-amount-sub-label">已用额度(元)</view>
        </view>
        <view class="bill-amount-sub-item">
          <text class="bill-amount-sub-value">{{ billDetail && billDetail.auAmount || '0.00' }}</text>
          <view class="bill-amount-sub-label">总额度(元)</view>
        </view>
      </view>
    </view>
    <!-- 账单列表 -->
    <scroll-view
      class="bill-list"
      scroll-y="true"
      :style="`height: ${scrollViewHeight}px;`"
    >
      <view v-for="(bill, idx) in bills" :key="idx" class="bill-item">
        <!-- 账单卡片内容 -->
        <view class="bill-item-header">
          <view>
            <view class="bill-item-title">{{ bill.date }} 借款{{ bill.amount }}元</view>
            <view class="bill-item-subtitle">关联订单 {{ bill.orderInfo }}</view>
          </view>
          <view class="bill-item-header-right" @tap="goToBill(bill)">共{{ bill.periods }}期
            <u-icon name="arrow-right" color="#000000" size="28"></u-icon>
          </view>
          
        </view>
        <view class="bill_line"></view>
        <view class="bill-item-detail">
          <view class="bill-item-detail-row">
            <view>
              <view class="bill-item-detail-label">第一期 应还金额</view>
              <view class="bill-item-detail-amount">{{ bill.repayAmount }}</view>
            </view>
            <view  class="bill-item-btn" @click="showRepayModal(bill)">
              {{ bill.overdue ? '逾期待还' : '立即还款' }}
            </view>
          </view>
          <view class="bill-item-detail-info">
            <view>还款日期 {{ bill.repayDate }}</view>
            <view class="bill-item-detail-time">仅支持每日06:00-23:00还款</view>
          </view>
          <view v-if="bill.overdue" class="bill-item-detail-overdue-bg">
            <text class="bill-item-detail-overdue">累计逾期1期 | 逾期应还{{ bill.overdueAmount }}元</text>
          </view>
        </view>
      </view>
    </scroll-view>
    <u-popup
      v-model="showCreditModal"
      mode="bottom"
      border-radius="16"
      :closeable="true"
    >
      <view class="credit-modal">
        <view class="modal-title">
          {{ currentRepayBill ? (currentRepayBill.overdue ? '逾期待还' : '立即还款') : '提前结清' }}
        </view>
        <view class="modal-amount">
          <text>¥ </text> {{ currentRepayBill ? currentRepayBill.repayAmount : '6525' }}
          <view class="plan-status-icon" @click="showPlanDetail(1)">
            <u-icon
              name="info-circle"
              color="#DDDDDD"
              size="48"
            ></u-icon> </view>
        </view>
        <view class="modal-section-title">还款方式</view>
        <view class="modal-bank-list">
          <view
            v-for="(bank, idx) in bankList"
            :key="idx"
            class="modal-bank-item"
            @click="selectBank(idx)"
          >
            <image
              src="/static/financial/bank-icon.png"
              style="width: 40rpx; height: 40rpx; margin-right: 16rpx"
            />
            {{ bank.name }} {{ bank.type }}({{ bank.last4 }})
            <template v-if="selectedBank === idx">
              <image src="/static/financial/step_Act.png" class="select-icon" />
            </template>
            <template v-else>
              <view class="yuan"></view>
            </template>
          </view>
        </view>
        <view
          class="modal-btn"
          @click="onSettle('O202507101943120927353950209')"
          >完成</view
        >
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getLoanListInfo } from '@/api/common.js';
import { formatISOTime } from '@/utils/filters.js';

export default {
  data() {
    return {
      scrollViewHeight: 0,
      bills: [
       
      ],
      showCreditModal: false,
      bankList: [
        { name: "招商银行", type: "储蓄卡", last4: "5755" },
        { name: "招商银行", type: "储蓄卡", last4: "5755" },
      ],
      selectedBank: null, // 选中的银行卡下标
      currentRepayBill: null, // 当前选中的还款账单
      billDetail: {
        waitRepayAmount: '0.00',
        usedAuAmount: '0.00',
        auAmount: '0.00'
      },
    };
  },
  mounted() {
    // 获取屏幕高度
    const systemInfo = uni.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight; // px

    const rpx2px = systemInfo.windowWidth / 750;
    const otherHeightPx = (74 + 236 + 44 + 40) * rpx2px;

    this.scrollViewHeight = windowHeight - otherHeightPx;

    // 获取借款记录
    this.loadLoanList();
  },
  methods: {
    // 加载借款记录
    loadLoanList() {
      uni.showLoading({
        title: '加载中...'
      });

      getLoanListInfo().then(res => {
        console.log('借款记录', res);
        if (res && res.data && res.data.success && res.data.result && res.data.result.loanInfos) {
          // 处理返回的借款记录数据
          this.billDetail = res.data.result;
          const loanInfos = res.data.result.loanInfos;
          this.bills = loanInfos.map(loan => {
           
            return {
              date: formatISOTime(loan.gmtCreate, '{y}-{m}-{d}') || '未知日期',
              amount: loan.amount || 0,
              periods: loan.periods || '未知',
              repayAmount: loan.amount || '0.00', // 这里可能需要根据实际业务逻辑调整
              repayDate: formatISOTime(loan.gmtPlanRepayCurPeriods, '{y}-{m}-{d}') || '未知日期',
              overdue: loan.overdueStatus === 'Y',
              overdueAmount: '0.00', // 这里可能需要根据实际业务逻辑计算逾期金额
              orderInfo: loan.remark || '未知订单', // 使用remark字段（商品名称）
              loanPurpose: loan.loanRemark || '未知用途', // 新增借款用途字段
              repaySource: loan.repayRemark || '未知来源', // 还款来源
              loanNo: loan.loanNo,
              bankName: loan.bankName,
              bankNum: loan.bankNum,
              status: loan.status,
              isAllRepay: loan.isAllRepay,
              id: loan.id, // 转换为字符串避免精度丢失
              gmtArrival: formatISOTime(loan.gmtArrival, '{y}-{m}-{d}') || '未知日期' // 保留放款时间
            };
          });
        }
      }).catch(err => {
        console.error('获取借款记录失败', err);
      }).finally(() => {
        uni.hideLoading();
      });
    },

    goToBill(bill){
        if (bill && bill.id) {
          uni.navigateTo({
            url: `/pages/financial/bill-detail?loanId=${bill.id}`
          });
        } else {
          uni.showToast({
            title: '缺少借款信息',
            icon: 'none',
            duration: 2000
          });
        }
    },
    selectBank(idx) {
      this.selectedBank = idx;
    },
    showRepayModal(bill) {
      this.currentRepayBill = bill;
      this.showCreditModal = true;
    },
    onSettle(val) {
      if (this.selectedBank === null) {
        uni.showToast({
          title: '请先选择银行卡',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      this.showCreditModal = false;
      if (val) {
        uni.navigateTo({
          url: "/pages/financial/cashier?order_sn=" + val,
        });
      }
    },
    showPlanDetail(plan) {
     
    },
  },
};
</script>

<style lang="scss" scoped>
.bill-page {
  padding: 20rpx 32rpx;
}

.bill-tip {
  width: 686rpx;
  height: 74rpx;
  background: #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  padding-left: 32rpx;
  image {
    width: 32rpx;
    height: 32rpx;
    margin: 0 16rpx;
  }
  .bill-tip-divider {
    width: 1px;
    height: 20rpx;
    background: #d9d9d9;
    margin: 0 24rpx 0 6rpx;
  }
}

.bill-amount-box {
  width: 686rpx;
  height: 236rpx;
  background: #ffffff;
  border-radius: 20rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx 0 rgba(0, 0, 0, 0.04);
  padding: 32rpx;
  margin-top: 20rpx;
  .bill-amount-main {
    display: flex;
    align-items: center;
    padding-bottom: 18rpx;
    border-bottom: 1rpx solid rgba(0,0,0,0.1);
    .bill-amount-main-value {
      font-size: 44rpx;
      font-weight: bold;
      color: #222;
      margin-left: 26rpx;
    }
    .bill-amount-main-label {
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
      
    }
  }
  .bill-amount-sub {
    display: flex;
    justify-content: space-evenly;
    margin-top: 20rpx;
    padding-bottom: 24rpx;
    .bill-amount-sub-item {
      text-align: center;
      border-right: 1rpx solid rgba(0,0,0,0.1);
      padding-right: 102rpx;
      .bill-amount-sub-value {
        font-size: 36rpx;
        color: #000000;
        font-weight: 500;
      }
      .bill-amount-sub-label {
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
      }
    }
    .bill-amount-sub-item:last-child {
        border-right: none;
        padding: 0;
    }

  }
}
.bill-list {
  margin-top: 20rpx;
  overflow-y: auto;
}
.bill-item {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 32rpx;
}
.bill-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  .bill-item-title {
    font-size: 28rpx;
    color: #222;
    font-weight: 400;
  }
  .bill-item-subtitle {
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    margin-top: 4rpx;
    margin-bottom: 32rpx;
  }
}
.bill_line {
    width: 100%;
    left: 0;
    position: absolute;
    border: 1rpx dashed rgba(0,0,0,0.1);

}
.bill-item-header-right {
    font-weight: 400;
    font-size: 24rpx;
    color: #000000;
  margin-bottom: 32rpx;
}
.bill-item-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  margin-top: 32rpx;
}
.bill-item-detail-label {
  font-size: 22rpx;
  color: #888;
}
.bill-item-detail-amount {
    font-weight: 500;
    font-size: 48rpx;
    color: #000000;
  margin: 4rpx 0;
}
.bill-item-btn {
    width: 164rpx;
    height: 64rpx;
    background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
    border-radius: 234rpx 234rpx 234rpx 234rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 64rpx;
}
.bill-item-detail-info {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
  margin-bottom: 12rpx;
  .bill-item-detail-time {
    margin-top: 2rpx;
  }
}
.bill-item-detail-overdue-bg {
    background: #FFF7F0;
  border-radius: 8rpx;
  padding: 20rpx 32rpx;
  margin-top: 8rpx;
}
.bill-item-detail-overdue {
    font-weight: 400;
font-size: 24rpx;
color: #FF5134;
}
.select-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: auto;
}
.yuan {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  margin-left: auto;
}
.credit-modal {
  padding: 32rpx 24rpx 24rpx 24rpx;
  .modal-title {
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    margin-bottom: 20rpx;
  }
  .modal-amount {
    font-weight: 600;
    font-size: 56rpx;
    color: #333333;
    text-align: center;
    margin-bottom: 24rpx;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    text {
      font-size: 32rpx;
      color: #333333;
      margin-bottom: 6rpx;
      margin-right: 8rpx;
    }
    .plan-status-icon {
      margin-left: 20rpx;
    }
  }
  .modal-section-title {
    font-weight: 400;
    font-size: 32rpx;
    color: #999999;
    margin: 24rpx 0 12rpx 0;
  }
  .modal-bank-list {
    border-radius: 12rpx;
    padding: 12rpx 0;
    margin-bottom: 12rpx;
  }
  .modal-bank-item {
    display: flex;
    align-items: center;
    padding: 18rpx 24rpx;
    font-size: 28rpx;
    color: #333;
    position: relative;
    &:last-child {
      border-bottom: none;
    }
    .modal-checkbox {
      margin-left: auto;
    }
  }
  .modal-btn {
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 78rpx 78rpx 78rpx 78rpx;
    font-weight: 400;
    font-size: 32rpx;
    color: #ffffff;
    text-align: center;
    line-height: 100rpx;
    margin: 24rpx auto 0;
  }
}
</style>
