<template>
  <view class="reviewing-page">
    <u-navbar title="审核中" :is-back="false"></u-navbar>
    <view class="header">
      <image
        src="/static/img/reviewing.png"
        mode="aspectFit"
        class="reviewing-icon"
      />
    </view>
    <view class="tips-view">
      <view>借款申请已提交</view>
      <view class="tips">审核完成后将发送短信通知，请耐心等待</view>
    </view>
    <view class="info-bottom">
      <u-button :custom-style="btnStyle" @click="goHome" class="submit-btn"
        >完成</u-button
      >
      <view class="info-safe-tip">
        <image src="/static/financial/desc.png" mode="scaleToFill" />
        平台承诺保护您的信息安全
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  computed: {
    btnStyle() {
      return {
        width: "100%",
        height: "100rpx",
        "border-radius": "86rpx",
        "font-size": "32rpx",
      };
    },
  },
  methods: {
    goHome() {
      uni.switchTab({
        url: "/pages/tabbar/home/<USER>",
      });
    },
    goProgress() {
      // 跳转到进度查询页面，如无可后续补充
      uni.navigateTo({
        url: "/pages/financial/faceAuth",
      });
    },
  },
  onBackPress() {
    // 拦截物理返回键，返回 true 阻止返回
    return true;
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #fff;
  height: 100vh;
  overflow: hidden;
}
.reviewing-page {
  background: #fff;
  height: 100vh;
  .header {
    image {
      width: 260rpx;
      height: 260rpx;
      display: block;
      margin: 132rpx auto 80rpx;
    }
  }
  .tips-view {
    text-align: center;
    font-weight: 400;
    font-size: 40rpx;
    color: #333333;
    margin-bottom: 80rpx;
    .tips {
      font-size: 28rpx;
      margin-top: 20rpx;
    }
  }
}

.info-bottom {
  width: 750rpx;
  height: 206rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  position: fixed;
  bottom: 0;
}
.submit-btn {
  width: 670rpx !important;
  height: 100rpx !important;
  margin-bottom: 16rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  margin: 32rpx auto 0;
  font-weight: 400;
  font-size: 32rpx;
  color: #ffffff !important;
  border: none !important;
  border-radius: 86rpx;
}
.info-safe-tip {
  text-align: center;
  color: #ff6b35;
  font-size: 24rpx;
  margin: 16rpx auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}
</style>
