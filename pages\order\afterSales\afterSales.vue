<template>
  <view class="content">
    <view class="u-tabs-box">
      <u-tabs
        bg-color="#fff"
        :list="list"
        :is-scroll="false"
        :current="current"
        @change="change"
        :active-color="$lightColor"
        :active-item-style="activeStyle"
      ></u-tabs>
    </view>
    <div class="u-tabs-search">
      <u-search
        placeholder="请输入订单编号/商品名称/售后单号"
        @search="submitSearchOrderList(current)"
        @clear="clear(current)"
        @custom="submitSearchOrderList(current)"
        v-model="keywords"
        bg-color="#F7F7F7"
        search-icon-color="#999"
      >
      </u-search>
    </div>
    <scroll-view class="body-view" scroll-y @scrolltolower="renderDate">
      <view
        class="seller-view"
        v-for="(order, orderIndex) in orderList"
        :key="orderIndex"
      >
        <!-- 订单编号 顶部左上角 -->
        <view class="order-sn-top" v-if="current === 0">
          订单编号：{{ order.sn }}
        </view>
        <view class="order-sn-top" v-else>
          售后单号：{{ order.service_sn || order.sn }}
        </view>
        <view class="order-sn-divider"></view>
        <!-- 店铺名称 -->
        <!-- <view class="seller-info u-flex u-row-between" v-if="current == 0">
          <view class="seller-name">
            <view class="name">{{ order.storeName }}</view>
          </view>
        </view> -->
        <!-- 申请记录 选项卡 -->
        <!-- <view class="seller-info u-flex u-row-between" v-if="current != 0">
          <view class="order-sn"
            >售后单号：{{ order.service_sn || order.sn }}</view
          >
          <view class="order-sn">{{ order.serviceType_text }}</view>
        </view> -->
        <view v-for="(sku, goodsIndex) in order.orderItems" :key="goodsIndex">
          <view class="goods-item-view" @click="onDetail(order, sku)">
            <view class="goods-img">
              <u-image
                border-radius="12"
                width="140rpx"
                height="140rpx"
                :src="sku.image"
              ></u-image>
            </view>
            {{ sku.specs }}
            <view class="goods-info">
              <view class="goods-title u-line-2">{{ sku.name }}</view>
            
              <view class="goods-specs">{{ formatSpecs(order.specs) }}   <text class="goods-num">x{{ sku.num }}</text></view>
              <view class="goods-price-num">
                <text class="goods-price"
                  >￥{{ order.flowPrice | unitPrice }}</text
                >
              
              </view>
            </view>
          </view>
          <view class="description">
            <!-- 售后申请  -->
            <view v-if="current === 0 && sku.afterSaleStatus">
              <view
                v-if="sku.afterSaleStatus && sku.afterSaleStatus=='ALREADY_APPLIED'"
                class="cannot_apply not_center"
              >
                <u-icon class="icon" name="info-circle-fill"></u-icon>
                该商品已申请售后服务
              </view>
            </view>
            <view v-if="current === 0 && sku.afterSaleStatus">
              <view
                v-if="sku.afterSaleStatus && sku.afterSaleStatus=='EXPIRED'"
                class="cannot_apply not_center"
                @click="tipsShow = true"
              >
                <u-icon class="icon" name="info-circle-fill"></u-icon>
                该商品无法申请售后
              </view>
            </view>
            <div v-if="current === 1 || current === 2">
              <!-- 申请中 -->
              <view
                class="order-status-bar"
                v-if="order.serviceType == 'RETURN_GOODS'"
              >
                退货处理-{{ order.serviceStatus | serviceStatusList }}
              </view>
              <view
                class="order-status-bar"
                v-if="order.serviceType == 'SUPPLY_AGAIN_GOODS'"
              >
                补发商品-{{ order.serviceStatus | serviceStatusList }}
              </view>
              <view
                class="order-status-bar"
                v-if="order.serviceType == 'RETURN_MONEY'"
              >
                退款-{{ order.serviceStatus | serviceStatusList }}
              </view>
              <view
                class="order-status-bar"
                v-if="order.serviceType == 'EXCHANGE_GOODS'"
              >
                换货-{{ order.serviceStatus | serviceStatusList }}
              </view>
              <view
                class="order-status-bar"
                v-if="order.serviceType == 'CANCEL'"
              >
                取消订单-{{ order.serviceStatus | serviceStatusList }}
              </view>
            </div>
          </view>
          <view class="btn-view">
            <view class="after-line">
              <!-- 申请中 -->
              <view
                class="default-btn border"
                v-if="
                  current === 2 &&
                  order.serviceStatus &&
                  order.serviceStatus == 'PASS' &&
                  order.serviceType != 'RETURN_MONEY'
                "
                @click="onExpress(order, sku)"
              >
                提交物流
              </view>
              <view @click="close(order, sku)"
               v-if="current == 1 && order.serviceStatus == 'APPLY' || order.serviceStatus == 'STORE_PASS'|| order.serviceStatus == 'AUDIT_PASS' || (order.penaltyPrice == 0 && order.serviceStatus == 'PASS')"
               class="close close-btn">
                取消售后
              </view>
              <view
                @click="payPenalty(order, sku)"
                v-if="current == 2 &&  order.serviceStatus == 'AUDIT_PASS'"
                class="close close-btn close-btn1"
              >
                支付违约金
              </view>
              <view
                @click="afterDetails(order, sku)"
                v-if="current === 1 || current === 2"
                class="default-btn orange-btn"
              >
                售后详情
              </view>
              <!-- 售后申请  -->
            
              <view
                @click="applyService(sku.sn, order, sku)"
                class="close close-btn close-btn1 apply-btn"
                 v-if="order.flowPrice != 0 && (order.orderStatus=='UNDELIVERED' || order.orderStatus=='DELIVERING' || order.orderStatus=='DELIVERED'|| order.orderStatus=='COMPLETED') && order.groupAfterSaleStatus != 'ALREADY_APPLIED'"
              >
                申请售后
              </view>
              <!-- <div class="sale" v-if="current === 0 && sku.afterSaleStatus">
                <div
                  v-if="
                    order.flowPrice != 0 &&
                    (sku.afterSaleStatus && Array.isArray(sku.afterSaleStatus) && (sku.afterSaleStatus.includes('NOT_APPLIED') ||
                      sku.afterSaleStatus.includes('PART_AFTER_SALE')))
                  "
                  @click="applyService(sku.sn, order, sku)"
                >
                  <view class="default-btn border"> 申请售后 </view>
                </div>
              </div> -->
            </view>
          </view>
        </view>
        <view
          v-if="
            current === 0 &&
            order.groupAfterSaleStatus &&
            order.groupAfterSaleStatus != 'ALREADY_APPLIED' &&
            order.orderItems.length >= 1
          "
          class="btn-view u-flex u-row-between"
        >
          <!-- 多个商品显示订单总价格 -->
          <!-- <view class="cannot_apply">
            订单总金额:<span class="countMoney">￥{{ order.flowPrice | unitPrice }}</span>
          </view> -->
        </view>
      </view>
      <u-loadmore bg-color="#f8f8f8" :status="status" />
    </scroll-view>
    <u-modal
      show-cancel-button
      :show-title="false"
      @confirm="closeService"
      v-model="cancelShow"
      content="确认取消售后"
      confirm-color="#ff7d20"
    ></u-modal>
    <u-modal
      v-model="tipsShow"
      content="当订单未确认收货|已过售后服务有效期|已申请售后服务时，不能申请售后"
    ></u-modal>
  </view>
</template>

<script>
import uniLoadMore from "@/components/uni-load-more/uni-load-more.vue";
import { getAfterSaleList, cancelAfterSale } from "@/api/after-sale.js";
import { getOrderList } from "@/api/order.js";
import storage from "@/utils/storage";

export default {
  components: {
    uniLoadMore,
  },
  data() {
    return {
      list: [
        //tab表头
        {
          name: "售后申请",
        },
        {
          name: "申请中",
        },
        {
          name: "申请记录",
        },
      ],
      current: 0, //当前表头索引
      tipsShow: false, //提示开关
      cancelShow: false, //取消显示开关
      selectedOrder: "", //选中的order
      orderList: [], //订单集合
      params: {
        pageNumber: 1,
        pageSize: 10,
        sort: "createTime",
        flowPrice: 0,
        order: "desc",
        afterSaleFlag:true,
      },

      logParams: {
        pageNumber: 1,
        pageSize: 10,
        afterSaleFlag:true,
      },
      status: "loadmore",
      keywords: "", // 搜索订单sn
      activeStyle:{
        fontWeight: "500",
      },
      pageOptions: {} // 存下来
    };
  },
  onLoad(options) {
    this.pageOptions = options; // 存下来
  },
  onShow() {
    this.orderList = [];
    this.params.pageNumber = 1;
    if (this.pageOptions && this.pageOptions.orderSn) {
      this.params.keywords = this.pageOptions.orderSn;
    }
    this.searchOrderList(this.current);
  },
  onPullDownRefresh() {
    this.change(this.current);
  },
  methods: {
    /**
     * 点击搜索执行搜索
     */
    submitSearchOrderList(current) {
      this.params.pageNumber = 1;
      this.logParams.pageNumber = 1;
      this.orderList = [];
      this.searchOrderList(current);
    },
    // 清空
    clear(current) {
      this.params.pageNumber = 1;
      this.logParams.pageNumber = 1;
      this.params.keywords = "";
      this.orderList = [];
      this.searchOrderList(current);
    },
    /**
     * 切换tab页时，初始化数据
     */
    change(index) {
      this.current = index;
      this.params.pageNumber = 1;
      this.params.pageSize = 10;
      // 保留 this.params.keywords
      this.orderList = [];
      //如果是2 则读取售后申请记录列表
      this.searchOrderList(index);
      uni.stopPullDownRefresh();
    },

    /**
     * 搜索初始化
     * 根据当前tab传值的索引进行更改
     */
    searchOrderList(index) {
      if (index == 0) {
        this.keywords ? (this.params.keywords = this.keywords) : "";
        this.getOrderList();
      } else {
        this.logParams = {
          pageNumber: 1,
          pageSize: 10,
          sort: "createTime",
          order: "desc",
          afterSaleFlag:true,
        };
        if (index === 1) {
          this.logParams.serviceStatus = "APPLY";
        }
        this.keywords ? (this.logParams.keywords = this.keywords) : "";
        this.orderList = [];
        this.getAfterSaleLogList();
      }
    },

    /**
     * 获取订单列表
     */
    getOrderList() {
      // uni.showLoading({
      //   title: "加载中",
      //   mask: true,
      // });
      getOrderList(this.params).then((res) => {
        // if (this.$store.state.isShowToast) {
        //   uni.hideLoading();
        // }
        const orderList = res.data.result.records;
        if (orderList.length > 0) {
          this.orderList = this.orderList.concat(orderList);
          this.params.pageNumber += 1;
        }
        if (orderList.length < 10) {
          this.status = "nomore";
        } else {
          this.status = "loading";
        }
      });
    },

    close(order, sku) {
      console.log(order, sku);
      this.selectedOrder = order;
      this.cancelShow = true;
    },

    async closeService() {
      uni.showLoading({
        title: "加载中",
      });
      console.log(this.selectedOrder);
      let res = await cancelAfterSale(this.selectedOrder.sn);
      if (res.data.success) {
        uni.showToast({
          title: "取消成功!",
          duration: 2000,
          icon: "none",
        });
      }
      this.orderList = [];
      this.searchOrderList(this.current);

      if (this.$store.state.isShowToast) {
        uni.hideLoading();
      }
    },

    /**
     * 售后详情
     */
    afterDetails(order) {
      uni.navigateTo({
        url: "./applyDetail?sn=" + order.sn,
      });
    },

    /**
     * 申请记录列表
     */
    getAfterSaleLogList() {
      getAfterSaleList(this.logParams).then((res) => {
        let afterSaleLogList = res.data.result.records;

        afterSaleLogList.forEach((item) => {
          item.orderItems = [
            {
              image: item.goodsImage,
              skuId: item.skuId,
              name: item.goodsName,
              num: item.num,
              price: item.flowPrice,
            },
          ];
        });

        this.orderList = this.orderList.concat(afterSaleLogList);

        if (afterSaleLogList.length < 10) {
          this.status = "nomore";
        } else {
          this.status = "loading";
        }
      });
    },

    /**
     * 申请售后
     */
    applyService(sn, order, sku) {
      console.log("sn",order,sku);
      // 判断发货状态
      if (order.orderStatus === "DELIVERING" || order.orderStatus === "UNDELIVERED") {
        // 未发货
        // 这里可以做你想做的逻辑，比如弹窗提示、跳转不同页面等
        uni.showToast({
          title: "该商品未发货",
          icon: "none",
        });
        // return; // 如果不想继续后续逻辑可以加 return
        uni.redirectTo({
          url: `./afterSalesDetail?sn=${sn}&value=${3}&flowPrice=${order.flowPrice}`,
        });
      } else if (
        order.orderStatus === "DELIVERED" ||
        order.orderStatus === "PARTS_DELIVERED" ||
        
        order.orderStatus === "COMPLETED"
      ) {
        console.log("已发货");
        
        // 已发货
        // 这里可以做你想做的逻辑，比如弹窗提示、跳转不同页面等
        uni.showToast({
          title: "该商品已发货",
          icon: "none",
        });
        // return; // 如果不想继续后续逻辑可以加 return
        uni.redirectTo({
          url: `./afterSalesDetail?sn=${sn}&value=${1}&flowPrice=${order.flowPrice}`,
        });
      }

      // 原有逻辑
      let data = {
        ...order,
        ...sku,
      };
      storage.setAfterSaleData(data);

      // uni.navigateTo({
      //   url: `/pages/order/afterSales/afterSalesSelect?sn=${sn}`,
      // });
    },

    /**
     * 提交物流信息
     */
    onExpress(order, sku) {
      sku.storeName = order.storeName;
      let data = {
        ...order,
        ...sku,
      };

      storage.setAfterSaleData(data);
      uni.navigateTo({
        url: `./afterSalesDetailExpress?serviceSn=${order.sn}`,
      });
    },

    /**
     * 查看详情
     */
    onDetail(goods, sku) {
      // 售后申请
      if (this.current == 0) {
        uni.navigateTo({
          url: `/pages/product/goods?id=${sku.skuId}&goodsId=${
            sku.goodsId || sku.goodsId
          }`,
        });
      } else {
        uni.navigateTo({
          url: `/pages/product/goods?id=${goods.skuId}&goodsId=${
            goods.goodsId || goods.goodsId
          }`,
        });
      }
    },

    /**
     * 底部加载数据
     */
    renderDate() {
      if (this.current === 0) {
        this.getOrderList();
      } else {
        this.logParams.pageNumber += 1;
        this.getAfterSaleLogList();
      }
    },

    /**
     * 支付违约金
     */
    payPenalty(order, sku) {
      console.log('支付违约金:', order, sku);
      
      // 检查是否有违约金需要支付
      if (order.serviceStatus === 'AUDIT_PASS') {
        // 跳转到支付页面，使用 after_sn 参数
        uni.navigateTo({
          url: `/pages/cart/payment/payOrder?after_sn=${order.sn}`
        });
      } else {
        uni.showToast({
          title: '暂无违约金需要支付',
          icon: 'none'
        });
      }
    },
    formatSpecs(specs) {
      if (!specs) return '无规格';
      // 如果是字符串，尝试解析
      if (typeof specs === 'string') {
        try {
          specs = JSON.parse(specs);
        } catch (e) {
          return '无规格';
        }
      }
      if (typeof specs !== 'object') return '无规格';
      const arr = Object.keys(specs)
        .filter(key => key !== 'images' && specs[key] !== undefined && specs[key] !== null && specs[key] !== '')
        .map(key => `${key}: ${specs[key]}`);
      return arr.length ? arr.join('；') : '无规格';
    },
  },
};
</script>

<style lang="scss">
page,
.content {
  background: $page-color-base;
  height: 100%;
}

.body-view {
  overflow-y: auto;
  // height: 100vh;
  height: calc(100vh - 44px - 80rpx - 104rpx);
  /* #ifdef APP-PLUS */
  height: calc(100vh - 44px - 116rpx);
  /* #endif */
  /* #ifdef h5 */
  
  /* #endif */
  
}

.u-tabs-search {
  padding: 16rpx 20rpx 20rpx 20rpx; 
  background: #fff;
}

.order-sn-top {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  // margin-bottom: 10rpx;
  margin-top: 20rpx;
  font-weight: 400;
  padding: 32rpx 0;
}
.order-sn-divider {
  height: 1rpx;
  background: #f2f3f5;
  // margin: 10rpx 0 20rpx 0;
  margin-bottom: 32rpx;
  border-radius: 2rpx;
}

.seller-view {
  width: 686rpx;
  background-color: #fff;
  margin: 0 auto 20rpx;
  padding: 0rpx 32rpx 32rpx 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx 0 rgba(0, 0, 0, 0.04);
}

.seller-info {
  height: 70rpx;
  .seller-name {
    font-size: 28rpx;
    display: flex;
    flex-direction: row;

    .name {
      margin-left: 15rpx;
      margin-top: -2rpx;
    }
  }
  .order-sn {
    font-size: 22rpx;
    color: #909399;
  }
}

.goods-item-view {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  background: none;
  border-radius: 0;
  margin-bottom: 0;
}
.goods-img {
  width: 148rpx;
  height: 148rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}
.goods-info {
  padding-left: 24rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.goods-title {
  width: 420rpx;
  color: #333;
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.goods-specs {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.goods-price-num {
  display: flex;
  align-items: baseline;
  margin-top: 8rpx;
}
.goods-price {
  color: #ff3a00;
  font-size: 32rpx;
  font-weight: 600;
  margin-right: 16rpx;
}
.goods-num {
  color: #bbb;
  font-size: 24rpx;
  margin-left: 8rpx;
}
.order-status-bar {
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 26rpx;
  border-radius: 18rpx;
  padding: 12rpx 20rpx;
  margin: 20rpx  0 32rpx;
  text-align: left;
  font-weight: 400;
}
.btn-view {
  display: flex;
  justify-content: flex-end;
  // margin-top: 28rpx;
  .after-line {
    display: flex;
  }
}
.default-btn {
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  color: #fff;
  border: none;
  border-radius: 200rpx;
  font-size: 24rpx;
  font-weight: 500;
  // padding: 10rpx 20rpx;
  width: 134rpx;
  height: 52rpx;
  text-align: center;
  line-height: 52rpx;
  // box-shadow: 0 4rpx 16rpx 0 rgba(255,125,32,0.08);
}
.close-btn {
  width: 134rpx;
  height: 52rpx;
  border-radius: 190rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  line-height: 48rpx;
  text-align: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  margin-left: 20rpx;
}
.close-btn1 {
  width: 156rpx;
}
.apply-btn {
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
}
.orange-btn {
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  border: none;
  margin-left: 20rpx;
}
.countMoney {
  margin-left: 7rpx;
  color: $main-color;
  font-size: 28rpx;
}
.description {
  color: #909399;
  font-size: 25rpx;
}
.cannot_apply {
  text-align: center;
  font-size: 22rpx;
  color: #999999;
  height: 58rpx;
  line-height: 58rpx;
  background: #f7f7f7;
  border-radius: 12rpx;
  margin-top: 20rpx;
}
.not_center {
  text-align: left;
}
.icon {
  margin: 0 10rpx;
}
.sale {
  display: flex;
  justify-content: flex-end;
  margin-left: 20rpx;
}

</style>
