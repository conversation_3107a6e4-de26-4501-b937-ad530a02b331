<template>
  <view class="mp-iphonex-bottom content">
    <u-form :model="form" ref="uForm">
      <view class="after-sales-goods-detail-view">
        <view>
          <view class="goods-item-view" @click="gotoGoodsDetail(sku)">
            <view class="goods-img">
              <u-image border-radius="8" width="148rpx" height="148rpx" :src="sku.image"></u-image>
            </view>
            <view class="goods-info">
              <view class="goods-title u-line-2">{{ sku.name }}</view>
              <view class="goods-specs">{{  formatSpecs(sku.specs) }}</view>
              <view class="goods-price">
                <span>￥{{ sku.price | unitPrice }}</span>
                <span class="num">申请售后数量: {{ sku.num }} </span>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="address-card">
        <view class="address-title">寄回地址</view>
        <view class="address-row">商家地址：{{ address }}</view>
        <view class="address-row">收货人：{{ consignee }}</view>
        <view class="address-row">联系方式：{{ mobile }}</view>
        <view class="address-row">商家备注：{{ sku.auditRemark }}</view>
      </view>

      <scroll-view scroll-y>
        <!-- 上传凭证 -->
        <view class="opt-view">
          <view class="img-title" style="font-size: 30rpx">填写物流信息</view>
          <u-form-item label="返回方式" :label-width="150">
            <div style="width: 100%; text-align: right;">快递至第三方卖家</div>
          </u-form-item>
          <u-form-item label="快递公司" :label-width="150">
            <div :style="{width: '100%', textAlign: 'right', color: form.courierCompany ? '#333333' : 'rgba(153,153,153,0.55)'}" @click="companySelectShow = true">
              {{ form.courierCompany || '请选择快递公司' }}
            </div>
            <u-icon name="arrow-right" :color="form.courierCompany ? '#333' : 'rgba(153,153,153,0.55)'" @click="companySelectShow = true" />
          </u-form-item>
          <u-form-item label="快递单号" :label-width="150">
            <u-input input-align="right" v-model="form.logisticsNo" placeholder="请输入快递单号" placeholder-style="color: rgba(153,153,153,0.55);"/>
          </u-form-item>
          <u-form-item label="发货时间" :label-width="150" class="no-border-item">
            <div :style="{width: '100%', textAlign: 'right', color: form.mDeliverTime ? '#333333' : 'rgba(153,153,153,0.55)'}" @click="timeshow = true">{{
                form.mDeliverTime || '请选择发货时间'
              }}
            </div>
          </u-form-item>
        </view>
        <view style="height:200rpx"></view>
      </scroll-view>
      
      <view class="submit-view">
        <u-button ripple :customStyle="{'background':$lightColor,'color':'#fff' }" shape="circle" @click="onSubmit">
          提交申请
        </u-button>
      </view>
    </u-form>
    <u-select mode="single-column" :list="companyList" v-model="companySelectShow"
              @confirm="companySelectConfirm" confirm-color="#FF5235"></u-select>
    <u-calendar v-model="timeshow" :mode="'date'" @change="onTimeChange" active-bg-color="#FF9500"></u-calendar>
    <u-toast ref="uToast"/>
  </view>
</template>

<script>

import {getLogistics} from "@/api/address.js";
import {fillShipInfo,getStoreAfterSaleAddress,} from "@/api/after-sale.js";
import storage from "@/utils/storage";

export default {
  data() {
    return {
      //快递公司 弹出框
      companySelectShow: false,
      companyList: [], //快递公司集合
      timeshow: false, //发货时间
      form: {
        courierCompany: "", //快递公司
        logisticsId: "", //快递公司ID
        logisticsNo: "", //快递单号
        mDeliverTime: "", //发货时间
      },
      serviceDetail: {}, //服务详情
      sku: {}, //sku信息
      address: '',
      consignee: '',
      mobile: '',
      remark: '',
    };
  },
  onLoad(options) {

    this.sku = storage.getAfterSaleData();
    let navTitle = "服务单详情";
    uni.setNavigationBarTitle({
      title: navTitle, //此处写页面的title
    });
    this.serviceDetail.sn = options.serviceSn;
    this.Logistics();
    this.getAddress();
  },
  methods: {
    /**
     * 确认快递公司
     */
    companySelectConfirm(e) {
      this.form.logisticsId = e[0].value;
      this.form.courierCompany = e[0].label;
    },

    /**
     * 获取快递公司
     */
    Logistics() {
      getLogistics().then((res) => {
        if (res.data.success) {
          res.data.result.forEach((item, index) => {
            this.companyList[index] = {
              value: item.id,
              label: item.name,
            };
          });
        }
      });
    },

    /**
     * 更改时间
     */
    onTimeChange(e) {
      this.form.mDeliverTime = e.result;
    },
    /**
     * 获取地址信息
     */
     getAddress() {
      getStoreAfterSaleAddress(this.serviceDetail.sn).then((res) => {
        if (res.data.success) {
          this.storeAfterSaleAddress = res.data.result;
          this.address = res.data.result.salesConsigneeAddressPath || '';
          this.consignee = res.data.result.salesConsigneeName || '';
          this.mobile = res.data.result.salesConsigneeMobile || '';
          this.remark = res.data.result.remark || '';
        }
      });
    },
    /**
     * 点击提交
     */
    onSubmit() {
      delete this.form.courierCompany;

      if (this.form.logisticsId == "") {
        this.$refs.uToast.show({
          title: "请选择快递公司",
          type: "error",
        });
        return;
      }
      if (this.form.logisticsNo == "") {
        this.$refs.uToast.show({
          title: "请填写快递单号",
          type: "error",
        });
        return;
      }
      if (this.form.mDeliverTime == "") {
        this.$refs.uToast.show({
          title: "请选择发货时间",
          type: "error",
        });
        return;
      }

      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      fillShipInfo(this.serviceDetail.sn, this.form).then((res) => {
        if (this.$store.state.isShowToast) {
          uni.hideLoading()
        }
        ;
        if (res.statusCode === 200) {
          this.$refs.uToast.show({
            title: "提交成功",
            type: "success",
          });
          setTimeout(() => { 
            uni.redirectTo({
              url: "/pages/order/afterSales/afterSales",
            });
          }, 100);
         
        }
      });
    },
    gotoGoodsDetail(sku) {
      uni.navigateTo({
        url: `/pages/product/goods?id=${sku.skuId}&goodsId=${sku.goodsId}`,
      });
    },
    formatSpecs(specs) {
      if (!specs) return '无规格';
      // 如果是字符串，尝试解析
      if (typeof specs === 'string') {
        try {
          specs = JSON.parse(specs);
        } catch (e) {
          return '无规格';
        }
      }
      if (typeof specs !== 'object') return '无规格';
      const arr = Object.keys(specs)
        .filter(key => key !== 'images' && specs[key] !== undefined && specs[key] !== null && specs[key] !== '')
        .map(key => `${key}: ${specs[key]}`);
      return arr.length ? arr.join('；') : '无规格';
    },
  },
};
</script>

<style lang="scss" scoped>
page,
.content {
  background: $page-color-base;
  height: 100%;
}

.mp-iphonex-bottom {
  // overflow: hidden;
}

.after-sales-goods-detail-view {
  height: 248rpx;
  background: linear-gradient( 180deg, #FF6634 0%, rgba(255,102,52,0) 100%);
  padding-top: 20rpx;

  .header {
    background-color: #f7f7f7;
    color: #999999;
    font-size: 22rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    line-height: 70rpx;

    .header-text {
      background-color: #999999;
      padding: 10rpx 30rpx;
      border-radius: 50rpx;
    }

    .seller-name {
      color: $main-color;
    }
  }

  .goods-item-view {
    width: 686rpx;
    margin: 0 auto;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.06);
    display: flex;
    align-items: center;
    padding: 24rpx;

    .goods-info {
      padding-left: 20rpx;
      flex: 1;

      .goods-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 400;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal;
        display: -webkit-box;
      }

      .goods-specs {
        font-size: 24rpx;
        margin-bottom: 10rpx;
        color: #cccccc;
      }

      .goods-price {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 28rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #FF5134;
        .num {
          font-weight: 400;
          font-size: 20rpx;
          color: #666666;
        }
      }
    }

    .goods-num {
      width: 60rpx;
      color: $main-color;
    }
  }
}

.opt-view {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 28rpx 28rpx 28rpx;
  margin: 24rpx auto;
  width: 90%;
  box-sizing: border-box;

  font-size: 26rpx;

  .how-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    border-bottom: 1px solid $page-color-base;
  }

  .explain-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 150rpx;
  }

  .img-title {
    height: 80rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
  }

  .images-view {
    padding: 20rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
  }
}

.submit-view {
  position: fixed;
  z-index: 999;
  bottom: 0;
  left: 0px;
  margin-top: 100rpx;
  height: 100rpx;
  width: 750rpx;
  align-items: center;
  padding: 0rpx 20rpx;
  color: #fff;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.address-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 28rpx 28rpx 28rpx;
  margin: 24rpx auto;
  width: 90%;
  box-sizing: border-box;
}
.address-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.address-row {
  font-size: 26rpx;
  color: #666;
  line-height: 44rpx;
  margin-bottom: 20rpx;
}
.logistics-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 28rpx 28rpx 28rpx;
  margin: 24rpx auto;
  width: 90%;
  box-sizing: border-box;
}
.logistics-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
}
.logistics-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 26rpx;
  color: #333;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.logistics-row:last-child {
  border-bottom: none;
  padding-bottom: 0;
  padding-top: 32rpx;
}
.logistics-label {
  flex: 0 0 180rpx;
  color: #666;
}
.logistics-value {
  flex: 1;
  text-align: right;
  color: #333;
}
.logistics-placeholder {
  color: rgba(153,153,153,0.55);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 8rpx;
}
.logistics-row-link {
  cursor: pointer;
  transition: background 0.2s;
}
.logistics-row-link:active {
  background: #f5f5f5;
}
.logistics-value.logistics-placeholder {
  color: rgba(153,153,153,0.55);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.logistics-company-text {
  display: inline-block;
  max-width: 70%;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: rgba(153,153,153,0.55);
}
.logistics-company-text.selected {
  color: #333;
}
.logistics-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  text-align: right;
  font-size: 26rpx;
  color: #333;
}
.logistics-input::placeholder {
  color: rgba(153,153,153,0.55) !important;
  font-size: 26rpx;
}
.reason-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.no-border-item {
  border-bottom: none !important;
}
.no-border-item /deep/ .u-form-item__body {
  border-bottom: none !important;
}
/deep/ .u-btn--primary {
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
}
/deep/ .u-size-default {
  width: 100%;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%) !important;
}
</style>
