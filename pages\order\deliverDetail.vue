<template>
  <view class="page-bg">
    <view class="logistics-header">
      <image class="logo" src="/static/img/logistics.png" mode="aspectFit" />
      <text class="company-sn">
        {{logisticsList.shipper || '未知快递公司'}}&nbsp;{{ logisticsList.logisticCode || '暂无运单号' }}
      </text>
      <view class="header-actions">
        <text class="header-btn" @click="copyNo(logisticsList.logisticCode)" v-if="logisticsList.logisticCode">复制</text>
        <!-- <view class="header-divider"></view>
        <text class="header-btn" @click="contactLogistics">联系物流</text> -->
      </view>
    </view>

   

    <view class="card address-card">
      <view class="address-row">
        <view class="address-icon">收</view>
        <text class="address-text address-order">{{ orderDetail.consigneeAddressPath || '未填写地址' }}{{ orderDetail.consigneeDetail || '' }}</text>
      </view>
      <view class="receiver-row">
        <text class="receiver">{{ orderDetail.consigneeName || '未填写姓名' }} {{ orderDetail.consigneeMobile | secrecyMobile }}</text>
        <view class="receiver-tags">
          <text class="tag_box1 tag-gray">号码保护中</text>
          <text class="tag_box1 tag-blue">已通过核验号码发货</text>
        </view>
      </view>
    </view>

    <view class="card logistics-card">
      
      <view class="logistics-list" v-if="logisticsList.traces && logisticsList.traces.length > 0">
        <view
          v-for="(item, idx) in logisticsList.traces"
          :key="idx"
          :class="['logistics-item', { 'no-border': idx === logisticsList.traces.length - 1 }]"
        >
          <view :class="['dot', { active: idx === 0 }]" />
          <view class="logistics-content">
            <text>{{ item.AcceptStation }}</text>
            <text class="desc">
              <text class="desc-bold">{{ item.AcceptTime }}</text>
            </text>
          </view>
        </view>
      </view>
      
      <view class="logistics-empty" v-else>
        <text>暂无物流信息</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getExpress } from "@/api/trade.js";
import { getOrderDetail } from "@/api/order.js";
export default {
  data() {
    return {
      orderDetail: {
        consigneeAddressPath: '',
        consigneeDetail: '',
        consigneeName: '',
        consigneeMobile: ''
      },
      logisticsList:{
        shipper:'',
        logisticCode:'',
        traces:[]
      }
    };
  },
  components: {},
  computed: {},
  onLoad(option) {
    let sn = option.order_sn;
    this.loadLogistics(sn);
    this.loadOrderDetail(sn);
  },
  mounted() {},
  methods: {
    // 获取订单详情
    loadOrderDetail(sn) {
      getOrderDetail(sn).then((res) => {
        if (res.data.success) {
          this.orderDetail = res.data.result.order;
        }
      });
    },
    // 获取物流信息
    loadLogistics(sn) {
      getExpress(sn).then((res) => {
        this.logisticsList = res.data.result;
        console.log(this.logisticsList);
      });
    },
    copyNo(no) {
      if (!no) {
        uni.showToast({ title: "暂无运单号", icon: "none" });
        return;
      }
      uni.setClipboardData({ 
        data: no,
        success: () => {
          uni.showToast({ title: "已复制", icon: "success" });
        },
        fail: () => {
          uni.showToast({ title: "复制失败", icon: "none" });
        }
      });
    },
    contactLogistics(pkg) {
      uni.makePhoneCall({ phoneNumber: pkg.logisticsPhone || "95338" });
    },
  },
};
</script>
<style>
page {
  background: #fff;
}
</style>

<style lang="scss" scoped>
.page-bg {
  background: #f5f6fa;
  // min-height: 100vh;
  // padding: 24rpx 0;
}

.card {
  background: #fff;

}

.express-card {
  padding-top: 32rpx;
  padding-bottom: 24rpx;
}

.express-row {
  display: flex;
  align-items: center;
}

.logo {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  background: #f5f5f5;
}

.express-title {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.company {
  font-weight: bold;
  font-size: 30rpx;
  color: #222;
}

.sn {
  color: #888;
  font-size: 24rpx;
  margin-top: 4rpx;
}

.copy-btn {
  font-size: 24rpx;
  color: #007aff;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 8rpx 24rpx;
  margin-left: 16rpx;
  border: none;
}

.address-card {
  margin-top: 0;
  padding: 24rpx 24rpx 18rpx 24rpx;
  display: flex;
  flex-direction: column;
  background: #fff;
  // border-radius: 16rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
}

.address-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.address-icon {
  width: 36rpx;
  height: 36rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 50%;
  text-align: center;
  line-height: 36rpx;
  font-size: 20rpx;
  margin-right: 12rpx;
  
}

.address-text {
  ffont-weight: 500;
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  line-height: 36rpx;
  
  &.address-order {
    width: 560rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.receiver-row {
  display: flex;
  align-items: center;
  margin-left: 50rpx;
}

.receiver {
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
}

.receiver-tags {
  display: flex;
  align-items: center;
}

.tag_box1 {
  height: 28rpx;
  font-size: 20rpx;
  border-radius: 6rpx;
  padding: 2rpx 8rpx !important;
  margin-left: 12rpx;
  background: #f5f5f5;
  color: #888;
  line-height: 28rpx;
}


.logistics-card {
  padding-top: 0;
  padding-bottom: 0;
}

.logistics-title {
  font-size: 28rpx;
  color: #ff5000;
  font-weight: bold;
  padding: 24rpx 0 0 0;
}

.logistics-list {
  position: relative;
  padding: 32rpx;
}

.logistics-item {
  position: relative;
  padding-left: 14rpx;
  min-height: 56rpx;
  border-left: 2rpx solid #FFCFC5;
  display: flex;
  align-items: flex-start;
  margin-bottom: 0;

  &.no-border {
    border-left: none;
  }
}

.dot {
  position: absolute;
  left: -9rpx;
  top: 0;
  width: 16rpx;
  height: 16rpx;
  background: #fff;
  border: 2rpx solid #FFCFC5;
  border-radius: 50%;
  z-index: 1;
  &.active {
    background: #ff5000;
    border-color: #ff5000;
    box-shadow: 0 0 8rpx #ff5000;
  }
}

.logistics-content {
  flex: 1;
  padding-bottom: 16rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.status {
  font-weight: 500;
  font-size: 32rpx;
  color: #666666;
  &.active {
    color: #FF5134;
  }
}

.desc {
  display: block;
  font-weight: 400;
  color: #666666;
  margin: 8rpx 0 0 0;
  font-size: 24rpx;
}

.desc-bold {
  font-weight: 400;
  font-size: 20rpx;
  color: #666;
  
}

.time {
  color: #666666;
  font-size: 22rpx;
  margin-top: 2rpx;
  display: block;
}

.logistics-header {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 32rpx;
  border-top: 1rpx solid rgba(0,0,0,0.1);
  border-bottom: 1rpx solid rgba(0,0,0,0.1);
}

.logo {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.company-sn {
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-actions {
  display: flex;
  align-items: center;
}

.header-btn {
  color: #888;
  font-size: 24rpx;
  cursor: pointer;
}
.header-btn:first-child {
  margin-left: 0;
}

.header-divider {
  width: 1rpx;
  height: 20rpx;
  background: #666666;
  margin: 4rpx 8rpx 0;
}

.logistics-empty {
  padding: 60rpx 32rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
</style>
