<template>
  <div class="wrapper">
    <!-- 楼层装修组件 -->
    <tpl ref="tpl" />
  </div>
</template>
<script>
import tpl from "@/pages/tabbar/home/<USER>";
import { isLogin } from "@/utils/filters.js";
import { checkEnjoyCardStatus } from "@/api/common.js";
import {mapMutations} from "vuex";

export default {
  data() {
    return {
      background: {
        backgroundColor: "#fff",
      },
    };
  },
  onShow(){
    // 检查臻享卡显示状态
		this.checkEnjoyCardStatus();
    setTimeout(()=>{
      this.$refs.tpl.fetchCoupon();
      // 只有登录后才调用获取未读消息数
      if (isLogin("auth")) {
        this.$refs.tpl.getUnreadCount();
      }
     
    },1000)
    
  },
  methods: {
    ...mapMutations(["login", "setEnjoyCardInfo"]),
    // 新增：检查臻享卡显示状态
    async checkEnjoyCardStatus() {
      try {
        const res = await checkEnjoyCardStatus();
        if (res && res.data) {
          // 存储整个data对象
          console.log(res.data);
          const obj = {
            desc:'臻享卡',
            hasIngLoan:true,
            login:true,
            showInst:false,
            status:'PASS_PAY',
            creditAmount:0,
            toRepayAmount:1000,
            overdueDays:5,
            desc:'8月20号重新申请'
          }
          this.setEnjoyCardInfo(obj);
          // this.setEnjoyCardInfo(res.data.data);
        } else {
          // 接口失败时设为null
          this.setEnjoyCardInfo(null);
        }
      } catch (error) {
        console.error('检查臻享卡显示状态失败:', error);
        this.setEnjoyCardInfo(null);
      }
    },
  },
  computed: {
    enjoyCardInfo() {
      return this.$store.state.enjoyCardInfo;
    },
    showEnjoyCard() {
      // 例如根据showInst或showMy判断
      return this.enjoyCardInfo && (this.enjoyCardInfo.showInst || this.enjoyCardInfo.showMy);
    }
  },
  onReachBottom(){
    // 给子级监听触底加载
    uni.$emit('onReachBottom',true)
  },
  
  onPullDownRefresh() {
    this.$refs.tpl.init();

    uni.stopPullDownRefresh();
  },
  components: {
    tpl,
  },
};
</script>

<style lang="scss" scoped>
</style>
