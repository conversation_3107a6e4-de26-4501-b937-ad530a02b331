<template>
  <view class="enjoy-card" v-if="shouldShowCard">
    <div class="enjoy-card-container" >
      <view class="card-content">
        <!-- 审核失败 -->
        <template v-if="status === 'failed'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount loan-title">审核失败</view>
          <u-button
            class="apply-button fail-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
          >
          <text class="gradient-text">{{ enjoyCardInfo.desc }}</text>
          </u-button>
         
        </template>

         <!-- 审核失败,已过拒绝期 -->
         <template v-if="status === 'failed1'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount loan-title">审核失败</view>
          <u-button
            class="apply-button"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            :hair-line="false"
            @click.stop="applyZhenCard('failed1')"
          >
          <text class="gradient-text">重新激活</text>
          </u-button>
         
        </template>

        <!-- 审核中 -->
        <template v-else-if="status === 'auditing'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount loan-title">审核中</view>
          <u-button
            class="apply-button auditing-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            disabled
          >
            <text class="audit-text">{{ auditingText }}</text>
          </u-button>
        </template>

        <!-- 未激活 -->
        <template v-else-if="status === 'inactive'">
          <view class="loan-label">预计最高购物额度(元)</view>
          <view class="loan-amount">{{ creditAmount }}<view class="unit">元</view></view>
          <u-button
            class="apply-button"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            :hair-line="false"
            @click.stop="applyZhenCard('inactive')"
          >
            <text class="gradient-text">立即申请</text>
          </u-button>
        </template>

        <!-- 已开通未用 -->
        <template v-else-if="status === 'opened_unused'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount">{{ creditAmount }}</view>
          <view class="card-arrow">
            <u-icon name="arrow-right" color="#FFFFFF" size="58"></u-icon>
          </view>
        </template>

        <!-- 额度过期 -->
        <template v-if="status === 'expired'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount loan-title">额度过期</view>
          <u-button
            class="apply-button expire-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            @click="activateLimit"
          >
            <text class="gradient-text">立即激活</text>
          </u-button>
        </template>

        <!-- 未到还款日 -->
        <template v-if="status === 'active'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount">{{ creditAmount }}</view>
          <u-button
            class="apply-button bill-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            @click="goBillDetail"
          >
            <text class="gradient-text">账单明细</text>
          </u-button>
         
        </template>

         <!-- 已到还款日常 -->
        <template v-if="status === 'repay'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount">{{ creditAmount }}</view>
          
          <u-button
            class="apply-button repay-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            @click="goRepay"
          >
            <text class="gradient-text">立即还款</text>
          </u-button>
        </template>

          <!-- 逾期 -->
        <template v-if="status === 'overdue'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount">{{ creditAmount }}</view>
          
          <u-button
            class="apply-button repay-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            @click="goRepay"
          >
            <text class="gradient-text">立即还款</text>
          </u-button>
        </template>

        <!-- 额度冻结 -->
        <template v-if="status === 'frozen'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount loan-title">额度冻结</view>
          <u-button
            class="apply-button bill-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            @click="goBillDetail"
          >
            <text class="gradient-text">账单明细</text>
          </u-button>
        </template>

        <!-- 额度冻结已逾期 -->
        <template v-if="status === 'frozen_overdue'">
          <view class="loan-label">可用额度(元)</view>
          <view class="loan-amount loan-title">额度冻结</view>
          <u-button
            class="apply-button repay-btn"
            type="warning"
            shape="circle"
            :custom-style="buttonStyle"
            @click="goRepay"
          >
            <text class="gradient-text">立即还款</text>
          </u-button>
        </template>
      </view>
      <div :class=" status === 'expired' ? 'card-features1' :'card-features'" v-if="status === 'inactive' || status === 'expired'">
        <div class="feature-item">
          <u-icon class="feature-icon" name="checkmark-circle" color="#fff" size="28"></u-icon>
          <text>官方质检</text>
        </div>
        <div class="feature-item">
          <u-icon class="feature-icon" name="checkmark-circle" color="#fff" size="28"></u-icon>
          <text>售后无忧</text>
        </div>
        <div class="feature-item">
          <u-icon class="feature-icon" name="checkmark-circle" color="#fff" size="28"></u-icon>
          <text>资质证明</text>
        </div>
      </div>
      <view class="card-text" v-if="status === 'opened_unused'">
        <text>{{ cardText }}<text style="margin-left: 8rpx;">{{ toRepayAmount }}</text></text>
      </view>
      <view class="card-text failTip-text" v-if="status === 'failed' || status === 'failed1'">
        <text>{{ failTip }}</text>
      </view>
      <view class="card-text auditingTip-text" v-if="status === 'auditing'">
        <text>{{ auditingTip }}</text>
      </view>
      <view class="card-text" v-if="status === 'active' || status === 'repay'">
        <text>{{ activeText }} <text style="margin-left: 8rpx;">{{ toRepayAmount }}</text></text>
        <!-- <view class="overdue-tag" v-if="status === 'active' && overdueDays > 0">
          <text>逾期 {{ overdueDays }} 天</text>
        </view> -->
      </view>
      <view class="card-text" v-if="status === 'overdue'">
        <text>{{ activeText }} <text style="margin-left: 8rpx;">{{ toRepayAmount }}</text></text>
        <view class="overdue-tag" v-if= "enjoyCardInfo.overdueDays > 0">
          <text>逾期 {{ enjoyCardInfo.overdueDays }} 天</text>
        </view>
      </view>
      <view class="card-text auditingTip-text" v-if="status === 'frozen'">
        <text>{{ frozenTip }}</text>
      </view>
      <view class="card-text auditingTip-text" v-if="status === 'frozen_overdue'">
        <text>{{ activeText }} <text style="margin-left: 8rpx;">{{ toRepayAmount }}</text></text>
        <view class="overdue-tag" v-if="enjoyCardInfo.overdueDays > 0">
          <text>逾期 {{enjoyCardInfo.overdueDays }} 天</text>
        </view>
      </view>
    </div>
  </view>
</template>

<script>
import { mapState } from 'vuex';
// 状态----》inactive: 未激活, opened_unused: 已开通未用, active: 正常, failed: 审核失败, 
// auditing: 审核中 , expired: 到期, frozen: 冻结, frozen_overdue: 逾期冻结
export default {
  data() {
    return {
      // status: 'inactive', // inactive: 未激活, opened_unused: 已开通未用, active: 正常, failed: 审核失败, auditing: 审核中,
      activeBtnType: 'bill', // bill: 账单明细, repay: 立即还款
      buttonStyle: {
        width: "200rpx",
        height: "64rpx",
        fontSize: "30rpx",
        lineHeight: "64rpx",
        margin: "0",
        background:"#fff",
      },
      cardText: '当前待还(元)',
      failText: '30日后可重申',
      failTip: '多多购物积累信用，可提升通过率',
      auditingText: '预计1分钟',
      auditingTip: '先享后付，分期购物',
      activeText:'当前待还(元)',
      overdueDays: 2, // 逾期天数，0表示未逾期
      frozenTip: '很抱歉，您的额度已冻结',
    };
  },
  computed: {
    ...mapState(['enjoyCardInfo']),
    shouldShowCard() {
      const info = this.enjoyCardInfo;
      if (!info) return false;
      return info.showInst === true || info.hasIngLoan === true;
    },
    status() {
      const info = this.enjoyCardInfo;
      if (!info || !info.status) return 'inactive';
      switch (info.status) {
        case 'NOT_LOGIN':      // 未登录
        case 'LOGIN':          // 已登录未开通
          return 'inactive';   // 未激活
        case 'AUDIT_ING':      // 审核中
          return 'auditing';
        case 'FAIL_ING':       // 审核失败,未过拒绝期
          return 'failed';
        case 'FAIL_OUT':       // 审核失败,已过拒绝期
          return 'failed1';
        case 'PASS':           // 审核通过,无借款
          return 'opened_unused';
        case 'PASS_NOPAY':     // 审核通过,未到还款日
          return 'active';
        case 'PASS_PAY':       // 审核通过,已到还款日repay
          return 'repay';
        case 'OVERDUE':        // 逾期
          return 'overdue';
        case 'FREEZE_HAVE':    // 冻结,有待还
          return 'frozen_overdue';
        case 'FREEZE_NO':      // 冻结,无代还
          return 'frozen';
        case 'OVERDATE':       // 过期
          return 'expired';
        default:
          return 'inactive';
      }
    },
    creditAmount() {
      return this.enjoyCardInfo && this.enjoyCardInfo.creditAmount != null
        ? this.enjoyCardInfo.creditAmount
        : '0.00';
    },
    toRepayAmount() {
      return this.enjoyCardInfo && this.enjoyCardInfo.toRepayAmount != null
        ? this.enjoyCardInfo.toRepayAmount
        : '0.00';
    }
  },
  mounted() {
    setTimeout(() => { 
      console.log('shouldShowCard:', this.shouldShowCard);
      console.log('enjoyCardInfo:', this.enjoyCardInfo);
    }, 500);
   
  },
  methods: {
    applyZhenCard(type){
      console.log('applyZhenCard:', type);
      if(type == 'inactive'){
        uni.navigateTo({
          url:'/pages/financial/idAuth',
        })
      }else{
        uni.navigateTo({
          url:'/pages/financial/faceAuth',
        })
      }
    
      
    },
    goUse() {
      // 跳转到额度使用页面
      uni.navigateTo({ url: '/pages/financial/useLimit' });
    },
    goBillDetail() {
      uni.navigateTo({ url: '/pages/financial/bill' });
    },
    goRepay() {
      uni.navigateTo({ url: '/pages/financial/cashier' });
    },
    activateLimit() {
      uni.navigateTo({  url:'/pages/financial/faceAuth', });
    }
  },
};
</script>

<style lang="scss" scoped>
.enjoy-card {
  // #ifdef H5
  // background: url("/static/enjoy_bg.png") no-repeat;
  // #endif
  background: url("/static/financial/bg.png");
  background-size: 100% 100%;
  height: 320rpx;
  z-index: 99;
  padding: 24rpx 22rpx 0 22rpx;
  box-sizing: border-box;
}
.enjoy-card-container {
  width: 708rpx;
  height: 292rpx;
  background: url("/static/financial/card_bg.png") no-repeat;
  background-size: 100% 100%;
  padding: 34rpx 40rpx 30rpx 56rpx;
  box-sizing: border-box;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  image {
    width: 44rpx;
    height: 44rpx;
  }
  .card-title {
    font-family: "FZZhengHeiS-EB-GB, FZZhengHeiS-EB-GB";
    font-weight: 600;
    font-size: 28rpx;
    color: #b06044;
    margin-left: 6rpx;
  }
}

.card-content {

  .loan-label {
    font-family: "PingFang SC, PingFang SC";
    font-weight: 500;
    font-size: 28rpx;
    color: #FFFFFF;
  }

  .loan-amount {
    position: relative;
    display: inline-block;
    font-size: 100rpx;
    color: #fff;
    font-family: 'DIN, DIN';
    font-weight: 500;
    .unit {
      position: absolute;
      top: 30rpx;   
      left: 100%;         
      transform: translateX(-50%); 
      width: 40rpx;
      height: 40rpx;
      background: #fff;
      border-radius: 50%;
      font-size: 28rpx;
      color: #FF8B28;
      text-align: center;
      line-height: 40rpx;
      font-weight: 600;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
      z-index: 1;
      /* #ifdef APP-PLUS */
      top: 20rpx;   
      z-index: 99;
      /* #endif */
    }
  }
  .loan-title {
    font-weight: 500;
    font-size: 64rpx;
    color: #FFFFFF;
    /* #ifdef H5 */
    margin-top: 20rpx;
    /* #endif */
    /* #ifdef APP-PLUS */
    margin-top: 38rpx;
    /* #endif */
   
  }
  .apply-button {
    flex-shrink: 0;
    position: absolute;
    top: 116rpx;
    right: 40rpx;
    text {
      font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 28rpx;
    }
   
  }
  .fail-btn {
    width: 308rpx !important;
    height: 64rpx !important;
    background: rgba(255,255,255,0.75) !important;
    box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(134,15,2,0.08);
    border-radius: 32rpx 32rpx 32rpx 32rpx;
  }
  .auditing-btn {
    background: rgba(255,255,255,0.75) !important;
    box-shadow: 0rpx 6rpx 16rpx 0rpx rgba(134,15,2,0.08);
    border-radius: 32rpx 32rpx 32rpx 32rpx;
  }
  .card-arrow {
    position: absolute;
    top: 116rpx;
    right: 40rpx;
  }
}

.card-features, .card-features1 {
  display: flex;
  justify-content: space-around;
  margin-top: 18rpx;
  position: relative;
  z-index: 99;
  /* #ifdef APP-PLUS */
  margin-top: 38rpx;
  /* #endif */
  .feature-item {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #666;
    image {
      width: 32rpx;
      height: 32rpx;
      display: block;
      margin-right: 6rpx;
    }
    .feature-icon {
      margin-top: 6rpx;
      margin-right: 6rpx;
      /* #ifdef APP-PLUS */
      margin-top: 0;
      /* #endif */
    }
    text {
      font-family: "PingFang SC, PingFang SC";
      font-weight: 400;
      font-size: 22rpx;
      color: #FFFFFF;
    }
  }
}
.card-features1 {
  margin-top: 46rpx;
}
.card-text {
  font-weight: 400;
  font-size: 24rpx;
  color: #FFFFFF;
  margin-top: 18rpx;
  /* #ifdef APP-PLUS */
  margin-top: 38rpx;
  /* #endif */
}
.failTip-text {
  margin-top: 48rpx;
}
.auditingTip-text {
  margin-top: 48rpx;
}
.gradient-text {
  background: linear-gradient(90deg, #FF5235 0%, #FF9500 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  /* 兼容性写法 */
  background-clip: text;
  color: transparent;
}
.audit-text {
  background: linear-gradient(0deg, #FF5235 0%, #FF9500 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  /* 兼容性写法 */
  background-clip: text;
  color: transparent;
}
.fail-btn {
  display: inline-block;
  padding: 0 24rpx;
  height: 48rpx;
  line-height: 48rpx;
  background: #fff3e0;
  color: #ff8b28;
  border-radius: 24rpx;
  font-size: 24rpx;
  margin: 16rpx 0 0 0;
  font-weight: 500;
}
.bill-btn {
  width: 200rpx !important;
  height: 64rpx !important;
  background: #fff !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 32rpx !important;
}
.bill-text {
  color: #ff8b28;
  font-size: 28rpx;
  font-weight: 500;
}
.repay-btn {
  width: 200rpx !important;
  height: 64rpx !important;
  background: #fff !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 32rpx !important;
}
.overdue-tag {
  display: inline-block;
  margin-left: 200rpx;
  padding: 0 18rpx;
  line-height: 32rpx;
  background: #ff3b30;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  vertical-align: middle;
  width: 144rpx;
  height: 38rpx;
  background: #F12711;
  box-shadow: 0rpx 6rpx 12rpx 0rpx rgba(134,15,2,0.12);
  border-radius: 6rpx 6rpx 6rpx 6rpx;
  border: 1rpx solid #FFFFFF;
}
.expire-btn {
  width: 200rpx !important;
  height: 64rpx !important;
  background: #fff !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 32rpx !important;
}
</style>
